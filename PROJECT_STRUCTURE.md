# 项目文件结构

本文档描述了GD32H757ZxT GPIO数据库系统的文件组织结构。

## 📁 文件夹结构

```
GD_GPIO/
├── 📁 excel/                          # Excel源文件
│   └── GD32H757ZxT_Datasheet_GPIO.xlsx
├── 📁 database/                       # 数据库文件
│   └── gpio_database.db               # 生成的SQLite数据库
├── 📁 tests/                          # 测试脚本
│   ├── __init__.py
│   ├── check_relationships.py         # 检查列关系
│   ├── explain_column_relationships.py # 解释列关系作用
│   ├── test_new_db.py                 # 测试新数据库
│   ├── test_simplified_db.py          # 测试简化后的数据库
│   └── verify_clean.py                # 验证数据清理
├── 📄 excel_to_database.py            # Excel到数据库转换器
├── 📄 gpio_database_manager.py        # 数据库管理器
├── 📄 gpio_query_tool.py              # 命令行查询工具
├── 📄 example_usage.py                # 使用示例
├── 📄 README.md                       # 项目说明文档
└── 📄 PROJECT_STRUCTURE.md            # 本文档
```

## 📋 文件分类说明

### 🔧 核心功能文件
- **excel_to_database.py**: 主要转换工具，将Excel文件转换为SQLite数据库
- **gpio_database_manager.py**: 数据库管理器，提供Python API接口
- **gpio_query_tool.py**: 命令行查询工具，提供便捷的查询命令

### 📊 数据文件
- **excel/**: 存放原始Excel数据文件
- **database/**: 存放生成的SQLite数据库文件

### 🧪 测试文件
- **tests/**: 存放所有测试和验证脚本
  - 功能测试脚本
  - 数据验证脚本
  - 性能测试脚本

### 📖 文档文件
- **README.md**: 项目主要说明文档
- **PROJECT_STRUCTURE.md**: 文件结构说明（本文档）
- **example_usage.py**: 使用示例和演示代码

## 🚀 使用方法

### 1. 生成数据库
```bash
python excel_to_database.py
```
这将在 `database/` 文件夹中生成 `gpio_database.db` 文件。

### 2. 使用查询工具
```bash
# 查看数据库摘要
python gpio_query_tool.py summary

# 查询特定引脚
python gpio_query_tool.py pin PA0

# 查找功能
python gpio_query_tool.py function TIMER1
```

### 3. 运行测试
```bash
# 进入tests目录
cd tests

# 运行特定测试
python test_simplified_db.py
python verify_clean.py
```

### 4. 使用Python API
```python
from gpio_database_manager import GPIODatabaseManager

# 创建管理器实例
db = GPIODatabaseManager("database/gpio_database.db")
db.connect()

# 进行查询
pin_info = db.query_by_pin_name('PA0')
print(pin_info)

db.close()
```

## 📝 文件依赖关系

```
excel_to_database.py
├── 读取: excel/GD32H757ZxT_Datasheet_GPIO.xlsx
└── 生成: database/gpio_database.db

gpio_database_manager.py
└── 使用: database/gpio_database.db

gpio_query_tool.py
├── 依赖: gpio_database_manager.py
└── 使用: database/gpio_database.db

tests/*.py
├── 依赖: gpio_database_manager.py
└── 使用: database/gpio_database.db

example_usage.py
├── 依赖: gpio_database_manager.py
└── 使用: database/gpio_database.db
```

## 🔄 工作流程

1. **数据准备**: 将Excel文件放入 `excel/` 文件夹
2. **数据转换**: 运行 `excel_to_database.py` 生成数据库
3. **数据查询**: 使用 `gpio_query_tool.py` 或 Python API 进行查询
4. **测试验证**: 运行 `tests/` 中的测试脚本验证功能
5. **学习使用**: 参考 `example_usage.py` 了解高级用法

## 📦 部署建议

### 开发环境
- 保持完整的文件结构
- 运行所有测试确保功能正常

### 生产环境
- 可以只部署核心文件：
  - `gpio_database_manager.py`
  - `gpio_query_tool.py`
  - `database/gpio_database.db`
- 可选择性包含：
  - `example_usage.py`（作为使用参考）
  - `README.md`（作为文档）

### 分发包
建议创建以下分发包：
1. **完整开发包**: 包含所有文件
2. **运行时包**: 只包含核心运行文件
3. **数据包**: 只包含数据库文件

## 🛠️ 维护说明

### 添加新测试
1. 在 `tests/` 文件夹中创建新的测试脚本
2. 添加适当的导入路径设置
3. 使用相对路径访问数据库文件

### 更新数据
1. 替换 `excel/` 中的Excel文件
2. 重新运行 `excel_to_database.py`
3. 运行测试验证数据完整性

### 扩展功能
1. 在相应的核心文件中添加新功能
2. 在 `tests/` 中添加对应的测试
3. 更新 `example_usage.py` 中的示例
4. 更新文档

这种文件组织结构使项目更加清晰、易于维护和扩展。
