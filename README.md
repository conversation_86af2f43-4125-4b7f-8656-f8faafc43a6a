# GD32H757ZxT GPIO数据库系统

这是一个用于管理和查询GD32H757ZxT微控制器GPIO配置的数据库系统。该系统从Excel文件中读取GPIO配置数据，生成SQLite数据库，并提供便捷的查询接口。

## 功能特点

- **Excel到数据库转换**: 自动读取Excel文件的所有工作表并生成关联数据库
- **同名列关联**: 自动识别并关联不同工作表中的同名列
- **多种查询方式**: 支持按引脚名称、功能名称、用途等多种方式查询
- **命令行工具**: 提供便捷的命令行查询接口
- **Python API**: 提供完整的Python编程接口

## 文件结构

```
├── 📁 excel/                               # Excel源文件
│   └── GD32H757ZxT_Datasheet_GPIO.xlsx    # 原始Excel数据文件
├── 📁 database/                            # 数据库文件
│   └── gpio_database.db                    # 生成的SQLite数据库
├── 📁 tests/                               # 测试脚本
│   ├── test_simplified_db.py               # 测试简化后的数据库
│   ├── verify_clean.py                     # 验证数据清理
│   └── ...                                 # 其他测试脚本
├── 📄 excel_to_database.py                 # Excel到数据库转换工具
├── 📄 gpio_database_manager.py             # 数据库管理器
├── 📄 gpio_query_tool.py                   # 命令行查询工具
├── 📄 example_usage.py                     # 使用示例
├── 📄 README.md                            # 本文档
└── 📄 PROJECT_STRUCTURE.md                 # 文件结构说明
```

## 安装依赖

```bash
pip install pandas openpyxl sqlite3
```

## 使用方法

### 1. 生成数据库

首次使用时，需要从Excel文件生成数据库：

```bash
python excel_to_database.py
```

这将读取 `excel/GD32H757ZxT_Datasheet_GPIO.xlsx` 文件并生成 `database/gpio_database.db` 数据库。

**重要改进**：
- ✅ **自动清理特殊字符**：转换过程中自动移除 `_x000D_` 等特殊字符
- ✅ **直观的表名**：数据库表名直接使用原始工作表名（如 `GPIO_AF` 而不是 `sheet_GPIO_AF`）
- ✅ **智能列关联**：自动识别并关联不同表中的同名列

### 2. 命令行查询

#### 查看数据库摘要
```bash
python gpio_query_tool.py summary
```

#### 查询特定引脚信息
```bash
python gpio_query_tool.py pin PA0
python gpio_query_tool.py pin PA0 --detailed  # 显示详细信息
```

#### 查找功能对应的引脚
```bash
python gpio_query_tool.py function TIMER1_CH0        # 模糊匹配
python gpio_query_tool.py function TIMER1_CH0 --exact # 精确匹配
```

#### 列出所有功能
```bash
python gpio_query_tool.py list-functions           # 列出所有功能
python gpio_query_tool.py list-functions --af AF1  # 只列出AF1的功能
```

#### 按用途查找引脚
```bash
python gpio_query_tool.py usage                # 列出所有引脚及用途
python gpio_query_tool.py usage TIMER          # 查找用途包含TIMER的引脚
```

#### 查看EXTI信息
```bash
python gpio_query_tool.py exti        # 显示所有EXTI信息
python gpio_query_tool.py exti 0      # 显示EXTI 0的信息
```

### 3. Python编程接口

```python
from gpio_database_manager import GPIODatabaseManager

# 创建数据库管理器
db_manager = GPIODatabaseManager("database/gpio_database.db")
db_manager.connect()

# 查询引脚信息
pin_info = db_manager.query_by_pin_name('PA0')
print(pin_info)

# 查询GPIO AF配置
af_config = db_manager.query_gpio_af('PA0')
print(af_config)

# 根据功能查找引脚
pins = db_manager.find_pins_by_function('TIMER1')
print(pins)

# 获取引脚定义
pin_defs = db_manager.get_pin_definitions('PA0')
print(pin_defs)

# 关闭连接
db_manager.close()
```

## 数据库结构

数据库包含以下主要表：

### 1. GPIO_Pin_definitions
存储GPIO引脚定义信息：
- `Pin_Name`: 引脚名称
- `Pins`: 物理引脚编号
- `Usage`: 用途
- `PCB_NAME`: PCB名称
- `Note`: 备注
- `Alternate`: 备用功能
- `Additional`: 附加信息

### 2. GPIO_AF
存储GPIO AF（Alternate Function）配置：
- `Pin_Name`: 引脚名称
- `AF0` ~ `AF15`: 各AF功能配置

### 3. EXTI
存储EXTI（External Interrupt）配置：
- `EXTI_ID`: EXTI编号
- `IRQn`: 中断编号
- `mode`: 模式
- `trig_type`: 触发类型
- `is_enabled`: 是否启用
- `Preempt_Priority`: 抢占优先级
- `Sub_Priority`: 子优先级

### 4. column_relationships
存储列关联关系，用于跨表查询：
- `column_name`: 列名
- `sheet_name`: 工作表名
- `table_name`: 数据库表名

## 查询示例

### 示例1：查找PA0引脚的所有信息
```bash
python gpio_query_tool.py pin PA0
```

输出：
```
查询引脚: PA0
==================================================
引脚定义:
  物理引脚: 34.0
  引脚名称: PA0
  用途: TIMER1_CH0
  PCB名称: LED_PWM_W
  备用功能: TIMER1_CH0,TIMER1_ETI,TIMER4_CH0,TIMER7_ETI,TIMER14_BRKIN0,SPI5_NSS,I2S5_WS,OSPIM_P0_IO6,USART1_CTS,UART3_TX,SDIO1_CMD,SAI1_SD1,EXMC_A19,TRIGSEL_IN0,EVENTOUT
  附加信息: ADC0_IN16,WKUP0

AF配置:
  AF1: TIMER1_CH0/TIMER1_ETI
  AF2: TIMER4_CH0
  AF3: TIMER7_ETI
  AF4: TIMER14_BRKIN0
  AF5: SPI5_NSS/I2S5_WS
  AF6: OSPIM_P0_IO6
  AF7: USART1_CTS
  AF8: UART3_TX
  AF9: SDIO1_CMD
  AF10: SAI1_SD1
  AF12: EXMC_A19
  AF13: TRIGSEL_IN0
  AF15: EVENTOUT
```

### 示例2：查找TIMER1_CH0功能对应的引脚
```bash
python gpio_query_tool.py function TIMER1_CH0
```

输出：
```
查找功能: TIMER1_CH0 (模糊匹配)
==================================================
找到 3 个匹配结果:
  PA0 - AF1: TIMER1_CH0/TIMER1_ETI
  PA15 - AF1: TIMER1_CH0/TIMER1_ETI
  PA5 - AF1: TIMER1_CH0/TIMER1_ETI
```

### 示例3：查找用途包含TIMER的引脚
```bash
python gpio_query_tool.py usage TIMER
```

这将列出所有用途字段包含"TIMER"的引脚。

## 高级功能

### 自定义SQL查询

可以使用数据库管理器执行自定义SQL查询：

```python
from gpio_database_manager import GPIODatabaseManager

db_manager = GPIODatabaseManager()
db_manager.connect()

# 执行自定义查询
results = db_manager.execute_custom_query("""
    SELECT p.Pin_Name, p.Usage, p.PCB_NAME, a.AF1, a.AF2
    FROM sheet_GPIO_Pin_definitions p
    LEFT JOIN sheet_GPIO_AF a ON p.Pin_Name = a.Pin_Name
    WHERE p.Usage LIKE '%TIMER%'
""")

for result in results:
    print(result)

db_manager.close()
```

### 列关联查询

系统自动识别同名列并建立关联关系。例如，`Pin Name` 列在多个表中出现，系统会自动建立关联，支持跨表查询。

## 扩展功能

### 添加新的工作表

如果Excel文件中添加了新的工作表，只需重新运行转换工具：

```bash
python excel_to_database.py
```

系统会自动检测新的工作表并更新数据库结构。

### 自定义查询工具

可以基于 `GPIODatabaseManager` 类开发自定义的查询工具：

```python
from gpio_database_manager import GPIODatabaseManager

class CustomGPIOQuery(GPIODatabaseManager):
    def find_pins_by_pcb_name(self, pcb_name):
        """根据PCB名称查找引脚"""
        return self.execute_custom_query(
            "SELECT * FROM sheet_GPIO_Pin_definitions WHERE PCB_NAME LIKE ?",
            (f'%{pcb_name}%',)
        )
    
    def get_timer_pins(self):
        """获取所有TIMER相关的引脚"""
        return self.execute_custom_query("""
            SELECT DISTINCT p.Pin_Name, p.Usage, p.PCB_NAME
            FROM sheet_GPIO_Pin_definitions p
            WHERE p.Usage LIKE '%TIMER%'
            ORDER BY p.Pin_Name
        """)
```

## 注意事项

1. **数据库文件**: 生成的 `gpio_database.db` 文件包含所有数据，可以独立使用
2. **Excel文件格式**: 确保Excel文件格式正确，列名不要包含特殊字符
3. **编码问题**: 系统自动处理中文字符，无需特殊设置
4. **性能**: 对于大量数据查询，建议使用索引优化

## 故障排除

### 常见问题

1. **Excel文件读取失败**
   - 检查文件路径是否正确
   - 确保Excel文件没有被其他程序占用
   - 验证Excel文件格式是否正确

2. **数据库连接失败**
   - 检查数据库文件是否存在
   - 确保有足够的磁盘空间
   - 检查文件权限

3. **查询结果为空**
   - 检查查询条件是否正确
   - 验证数据是否已正确导入
   - 使用 `summary` 命令检查数据库状态

## 更新日志

- **v2.0.0**: 重大改进版本
  - ✅ **自动清理特殊字符**：转换时自动移除 `_x000D_` 等特殊字符
  - ✅ **直观表名**：使用原始工作表名作为数据库表名
  - ✅ **智能表名清理**：自动处理空格和特殊字符，确保SQL兼容性
  - ✅ **动态表名解析**：数据库管理器自动适配表名变化
- **v1.0.0**: 初始版本，支持Excel到数据库转换和基本查询功能
  - 支持6个工作表的数据导入
  - 提供命令行和Python API接口
  - 自动识别列关联关系

## 贡献

欢迎提交问题和改进建议！
