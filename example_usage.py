#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GPIO数据库使用示例

演示如何使用GPIO数据库系统进行各种查询操作
"""

from gpio_database_manager import GPIODatabaseManager
import json

def print_separator(title):
    """打印分隔符"""
    print(f"\n{'='*60}")
    print(f"{title}")
    print('='*60)

def example_basic_queries():
    """基本查询示例"""
    print_separator("基本查询示例")
    
    # 创建数据库管理器
    db_manager = GPIODatabaseManager("database/gpio_database.db")
    
    if not db_manager.connect():
        print("无法连接到数据库")
        return
    
    try:
        # 1. 查询特定引脚的所有信息
        print("\n1. 查询PA0引脚的所有信息:")
        pa0_info = db_manager.query_by_pin_name('PA0')
        
        for sheet_name, records in pa0_info.items():
            print(f"\n  {sheet_name}:")
            for record in records:
                for key, value in record.items():
                    if key != 'id' and value and str(value).strip():
                        print(f"    {key}: {value}")
        
        # 2. 查询GPIO AF配置
        print("\n2. 查询PA0的AF配置:")
        af_config = db_manager.query_gpio_af('PA0')
        if af_config:
            record = af_config[0]
            print(f"  引脚: {record['Pin_Name']}")
            for af_num in range(16):
                af_col = f'AF{af_num}'
                if af_col in record and record[af_col] and record[af_col].strip():
                    print(f"    {af_col}: {record[af_col]}")
        
        # 3. 根据功能查找引脚
        print("\n3. 查找TIMER1相关功能的引脚:")
        timer_pins = db_manager.find_pins_by_function('TIMER1')
        if timer_pins:
            print(f"  找到 {len(timer_pins)} 个相关引脚:")
            for pin in timer_pins[:10]:  # 只显示前10个
                print(f"    {pin['Pin_Name']} - {pin['AF_Number']}: {pin['Function_Name']}")
            if len(timer_pins) > 10:
                print(f"    ... 还有 {len(timer_pins) - 10} 个")
        
    finally:
        db_manager.close()

def example_advanced_queries():
    """高级查询示例"""
    print_separator("高级查询示例")
    
    db_manager = GPIODatabaseManager()
    
    if not db_manager.connect():
        print("无法连接到数据库")
        return
    
    try:
        # 1. 查找所有SPI相关的引脚
        print("\n1. 查找所有SPI相关的引脚:")
        spi_pins = db_manager.find_pins_by_function('SPI')
        if spi_pins:
            spi_groups = {}
            for pin in spi_pins:
                func = pin['Function_Name']
                if 'SPI' in func:
                    spi_num = func.split('_')[0] if '_' in func else func
                    if spi_num not in spi_groups:
                        spi_groups[spi_num] = []
                    spi_groups[spi_num].append(pin)
            
            for spi_name, pins in sorted(spi_groups.items()):
                print(f"\n  {spi_name}:")
                for pin in sorted(pins, key=lambda x: x['Pin_Name']):
                    print(f"    {pin['Pin_Name']} - {pin['AF_Number']}: {pin['Function_Name']}")
        
        # 2. 查找所有TIMER相关的引脚并按TIMER分组
        print("\n2. 查找所有TIMER相关的引脚（按TIMER分组）:")
        timer_pins = db_manager.find_pins_by_function('TIMER')
        if timer_pins:
            timer_groups = {}
            for pin in timer_pins:
                func = pin['Function_Name']
                if 'TIMER' in func:
                    # 提取TIMER编号
                    timer_part = func.split('_')[0] if '_' in func else func
                    if timer_part not in timer_groups:
                        timer_groups[timer_part] = []
                    timer_groups[timer_part].append(pin)
            
            # 只显示前5个TIMER组
            for i, (timer_name, pins) in enumerate(sorted(timer_groups.items())):
                if i >= 5:
                    print(f"  ... 还有 {len(timer_groups) - 5} 个TIMER组")
                    break
                print(f"\n  {timer_name}:")
                for pin in sorted(pins, key=lambda x: x['Pin_Name'])[:5]:  # 每组最多显示5个
                    print(f"    {pin['Pin_Name']} - {pin['AF_Number']}: {pin['Function_Name']}")
                if len(pins) > 5:
                    print(f"    ... 还有 {len(pins) - 5} 个引脚")
        
        # 3. 自定义SQL查询 - 查找有PCB名称的引脚
        print("\n3. 查找有PCB名称的引脚:")
        pcb_pins = db_manager.execute_custom_query("""
            SELECT Pin_Name, Usage, PCB_NAME 
            FROM sheet_GPIO_Pin_definitions 
            WHERE PCB_NAME IS NOT NULL AND PCB_NAME != '' 
            ORDER BY PCB_NAME
        """)
        
        if pcb_pins:
            print(f"  找到 {len(pcb_pins)} 个有PCB名称的引脚:")
            pcb_groups = {}
            for pin in pcb_pins:
                pcb_name = pin['PCB_NAME']
                if pcb_name not in pcb_groups:
                    pcb_groups[pcb_name] = []
                pcb_groups[pcb_name].append(pin)
            
            # 显示前10个PCB组
            for i, (pcb_name, pins) in enumerate(sorted(pcb_groups.items())):
                if i >= 10:
                    print(f"  ... 还有 {len(pcb_groups) - 10} 个PCB组")
                    break
                print(f"    {pcb_name}: {', '.join([p['Pin_Name'] for p in pins])}")
        
    finally:
        db_manager.close()

def example_data_analysis():
    """数据分析示例"""
    print_separator("数据分析示例")
    
    db_manager = GPIODatabaseManager()
    
    if not db_manager.connect():
        print("无法连接到数据库")
        return
    
    try:
        # 1. 统计各AF的功能数量
        print("\n1. 统计各AF的功能数量:")
        af_stats = {}
        af_info = db_manager.query_gpio_af()
        
        for record in af_info:
            for af_num in range(16):
                af_col = f'AF{af_num}'
                if af_col in record and record[af_col] and record[af_col].strip():
                    if af_col not in af_stats:
                        af_stats[af_col] = set()
                    af_stats[af_col].add(record[af_col])
        
        for af_name in sorted(af_stats.keys()):
            print(f"  {af_name}: {len(af_stats[af_name])} 个不同功能")
        
        # 2. 统计引脚用途分布
        print("\n2. 统计引脚用途分布:")
        pin_defs = db_manager.get_pin_definitions()
        usage_stats = {}
        
        for pin in pin_defs:
            usage = pin.get('Usage', 'None')
            if usage:
                # 提取主要功能类型
                if 'TIMER' in usage:
                    main_type = 'TIMER'
                elif 'GPIO' in usage:
                    main_type = 'GPIO'
                elif 'TLI' in usage:
                    main_type = 'TLI'
                elif 'SPI' in usage:
                    main_type = 'SPI'
                elif 'I2C' in usage:
                    main_type = 'I2C'
                elif 'USART' in usage or 'UART' in usage:
                    main_type = 'UART/USART'
                elif 'ADC' in usage:
                    main_type = 'ADC'
                elif usage == 'None':
                    main_type = 'None'
                else:
                    main_type = 'Other'
                
                usage_stats[main_type] = usage_stats.get(main_type, 0) + 1
        
        for usage_type, count in sorted(usage_stats.items(), key=lambda x: x[1], reverse=True):
            print(f"  {usage_type}: {count} 个引脚")
        
        # 3. 查找多功能引脚（有多个AF配置的引脚）
        print("\n3. 查找多功能引脚（前10个）:")
        multi_func_pins = []
        
        for record in af_info:
            af_count = 0
            af_functions = []
            for af_num in range(16):
                af_col = f'AF{af_num}'
                if af_col in record and record[af_col] and record[af_col].strip():
                    af_count += 1
                    af_functions.append(f"{af_col}:{record[af_col]}")
            
            if af_count > 5:  # 有超过5个AF功能的引脚
                multi_func_pins.append({
                    'pin_name': record['Pin_Name'],
                    'af_count': af_count,
                    'functions': af_functions
                })
        
        # 按AF数量排序
        multi_func_pins.sort(key=lambda x: x['af_count'], reverse=True)
        
        for i, pin in enumerate(multi_func_pins[:10]):
            print(f"  {pin['pin_name']}: {pin['af_count']} 个AF功能")
            for func in pin['functions'][:3]:  # 只显示前3个功能
                print(f"    {func}")
            if len(pin['functions']) > 3:
                print(f"    ... 还有 {len(pin['functions']) - 3} 个功能")
        
    finally:
        db_manager.close()

def example_practical_use_cases():
    """实际使用场景示例"""
    print_separator("实际使用场景示例")
    
    db_manager = GPIODatabaseManager()
    
    if not db_manager.connect():
        print("无法连接到数据库")
        return
    
    try:
        # 场景1: 为LCD显示器查找所有相关引脚
        print("\n场景1: 查找LCD显示器相关的所有引脚")
        lcd_pins = db_manager.execute_custom_query("""
            SELECT Pin_Name, Usage, PCB_NAME, Alternate
            FROM sheet_GPIO_Pin_definitions 
            WHERE PCB_NAME LIKE '%LCD%' OR Usage LIKE '%TLI%'
            ORDER BY Pin_Name
        """)
        
        if lcd_pins:
            print(f"  找到 {len(lcd_pins)} 个LCD相关引脚:")
            for pin in lcd_pins[:15]:  # 显示前15个
                pcb_name = pin['PCB_NAME'] or 'N/A'
                print(f"    {pin['Pin_Name']:8} - {pin['Usage']:15} (PCB: {pcb_name})")
            if len(lcd_pins) > 15:
                print(f"    ... 还有 {len(lcd_pins) - 15} 个引脚")
        
        # 场景2: 查找可用于PWM输出的引脚
        print("\n场景2: 查找可用于PWM输出的引脚（TIMER通道）")
        pwm_pins = db_manager.find_pins_by_function('CH')  # 查找包含CH（通道）的功能
        
        if pwm_pins:
            pwm_timers = {}
            for pin in pwm_pins:
                func = pin['Function_Name']
                if 'TIMER' in func and 'CH' in func:
                    timer_name = func.split('_')[0]
                    if timer_name not in pwm_timers:
                        pwm_timers[timer_name] = []
                    pwm_timers[timer_name].append(pin)
            
            print(f"  找到 {len(pwm_timers)} 个TIMER可用于PWM:")
            for i, (timer_name, pins) in enumerate(sorted(pwm_timers.items())):
                if i >= 8:  # 只显示前8个TIMER
                    print(f"  ... 还有 {len(pwm_timers) - 8} 个TIMER")
                    break
                channels = sorted(set([p['Function_Name'].split('_')[1] for p in pins if '_' in p['Function_Name']]))
                print(f"    {timer_name}: {len(pins)} 个引脚, 通道: {', '.join(channels[:4])}")
        
        # 场景3: 查找串口通信相关引脚
        print("\n场景3: 查找串口通信相关引脚")
        uart_pins = []
        uart_pins.extend(db_manager.find_pins_by_function('USART'))
        uart_pins.extend(db_manager.find_pins_by_function('UART'))
        
        if uart_pins:
            uart_groups = {}
            for pin in uart_pins:
                func = pin['Function_Name']
                uart_name = func.split('_')[0]
                if uart_name not in uart_groups:
                    uart_groups[uart_name] = {'TX': [], 'RX': [], 'Other': []}
                
                if 'TX' in func:
                    uart_groups[uart_name]['TX'].append(pin)
                elif 'RX' in func:
                    uart_groups[uart_name]['RX'].append(pin)
                else:
                    uart_groups[uart_name]['Other'].append(pin)
            
            print(f"  找到 {len(uart_groups)} 个UART/USART:")
            for uart_name, pins in sorted(uart_groups.items()):
                tx_pins = [p['Pin_Name'] for p in pins['TX']]
                rx_pins = [p['Pin_Name'] for p in pins['RX']]
                other_pins = [p['Pin_Name'] for p in pins['Other']]
                
                print(f"    {uart_name}:")
                if tx_pins:
                    print(f"      TX: {', '.join(tx_pins)}")
                if rx_pins:
                    print(f"      RX: {', '.join(rx_pins)}")
                if other_pins:
                    print(f"      其他: {', '.join(other_pins)}")
        
    finally:
        db_manager.close()

def main():
    """主函数"""
    print("GPIO数据库使用示例")
    print("本示例演示了如何使用GPIO数据库系统进行各种查询操作")
    
    # 运行各种示例
    example_basic_queries()
    example_advanced_queries()
    example_data_analysis()
    example_practical_use_cases()
    
    print_separator("示例完成")
    print("更多使用方法请参考 README.md 文档")
    print("或使用命令行工具: python gpio_query_tool.py --help")

if __name__ == "__main__":
    main()
