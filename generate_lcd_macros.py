#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成LCD相关的C语言宏定义
筛选出数据库内PCB Pin Name为LCD开头，且Usage为TLI开头的引脚
根据Pin Name和PCB Pin Name来生成宏定义
"""

from gpio_database_manager import GPIODatabaseManager
import re

def extract_pin_info(pin_name):
    """
    从引脚名称中提取端口和引脚号
    例如: PG13 -> ('G', '13')
    """
    match = re.match(r'P([A-Z])(\d+)', pin_name)
    if match:
        return match.group(1), match.group(2)
    return None, None

def get_af_for_pin_and_function(db_manager, pin_name, function_name):
    """
    查找指定引脚和功能对应的AF值
    """
    cursor = db_manager.conn.cursor()
    
    # 查询GPIO_AF表中该引脚的所有AF配置
    cursor.execute('SELECT * FROM GPIO_AF WHERE Pin_Name = ?', (pin_name,))
    row = cursor.fetchone()
    
    if not row:
        return None
    
    # 检查每个AF列，看是否包含指定的功能
    af_columns = ['AF0', 'AF1', 'AF2', 'AF3', 'AF4', 'AF5', 'AF6', 'AF7',
                  'AF8', 'AF9', 'AF10', 'AF11', 'AF12', 'AF13', 'AF14', 'AF15']
    
    for af_col in af_columns:
        af_value = row[af_col]
        if af_value and function_name in af_value:
            # 提取AF号码
            af_number = af_col.replace('AF', '')
            return af_number
    
    return None

def generate_macro_name(pcb_pin_name):
    """
    从PCB Pin Name生成宏定义名称
    例如: LCD_R0 -> LCD_R0
    """
    # 确保以LCD_开头
    if pcb_pin_name.startswith('LCD_'):
        return pcb_pin_name  # 保持原样
    else:
        return f"LCD_{pcb_pin_name}"

def generate_c_macros():
    """
    生成C语言宏定义
    """
    # 连接数据库
    db = GPIODatabaseManager('database/gpio_database.db')
    if not db.connect():
        print("❌ 无法连接到数据库")
        return
    
    print("✅ 数据库连接成功")
    
    # 查询符合条件的引脚
    cursor = db.conn.cursor()
    cursor.execute('''
        SELECT Pin_Name, Usage, PCB_Pin_Name 
        FROM GPIO_Pin_definitions 
        WHERE PCB_Pin_Name LIKE "LCD%" AND Usage LIKE "TLI%"
        ORDER BY PCB_Pin_Name
    ''')
    
    pins = cursor.fetchall()
    print(f"找到 {len(pins)} 个符合条件的引脚")
    
    # 生成宏定义
    macros = []
    macros.append("// LCD TLI GPIO 宏定义")
    macros.append("// 自动生成，请勿手动修改")
    macros.append("")
    
    for pin_data in pins:
        pin_name = pin_data[0]
        usage = pin_data[1]
        pcb_pin_name = pin_data[2]
        
        # 提取端口和引脚号
        port, pin_num = extract_pin_info(pin_name)
        if not port or not pin_num:
            print(f"⚠️  无法解析引脚名称: {pin_name}")
            continue
        
        # 获取AF值
        af_number = get_af_for_pin_and_function(db, pin_name, usage)
        if af_number is None:
            print(f"⚠️  未找到 {pin_name} 的 {usage} 对应的AF值")
            continue
        
        # 生成宏名称
        macro_name = generate_macro_name(pcb_pin_name)
        
        # 生成宏定义
        macros.append(f"// {pin_name} - {usage}")
        macros.append(f"#define {macro_name}_GPIO_PORT   GPIO{port}")
        macros.append(f"#define {macro_name}_GPIO_CLK    RCU_GPIO{port}")
        macros.append(f"#define {macro_name}_GPIO_PIN    GPIO_PIN_{pin_num}")
        macros.append(f"#define {macro_name}_GPIO_AF     GPIO_AF_{af_number}")
        macros.append("")
    
    # 输出结果
    print("\n" + "="*60)
    print("生成的C语言宏定义:")
    print("="*60)
    for macro in macros:
        print(macro)
    
    # 保存到文件
    with open('lcd_gpio_macros.h', 'w', encoding='utf-8') as f:
        f.write('\n'.join(macros))
    
    print(f"\n✅ 宏定义已保存到 lcd_gpio_macros.h 文件")
    
    db.close()

if __name__ == "__main__":
    generate_c_macros()
