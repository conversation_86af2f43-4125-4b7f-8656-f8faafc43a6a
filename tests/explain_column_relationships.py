#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
解释 column_relationships 表的作用和用法
"""

from gpio_database_manager import GPIODatabaseManager

def main():
    print("column_relationships 表的作用和用法")
    print("=" * 60)
    
    db = GPIODatabaseManager()
    if not db.connect():
        return
    
    try:
        # 1. 显示 column_relationships 表的内容
        print("\n1. column_relationships 表的内容:")
        print("-" * 40)
        relationships = db.execute_custom_query("SELECT * FROM column_relationships ORDER BY column_name, sheet_name")
        
        if relationships:
            print(f"{'列名':<15} {'工作表名':<20} {'数据库表名':<25}")
            print("-" * 60)
            for row in relationships:
                print(f"{row['column_name']:<15} {row['sheet_name']:<20} {row['table_name']:<25}")
        
        # 2. 解释表的作用
        print(f"\n2. 表的主要作用:")
        print("-" * 40)
        print("✓ 记录哪些列名在多个工作表中出现")
        print("✓ 建立不同表之间的关联关系")
        print("✓ 支持跨表查询和数据关联")
        print("✓ 为数据库管理器提供元数据信息")
        
        # 3. 展示同名列的分组
        print(f"\n3. 同名列分组:")
        print("-" * 40)
        column_groups = {}
        for row in relationships:
            col_name = row['column_name']
            if col_name not in column_groups:
                column_groups[col_name] = []
            column_groups[col_name].append({
                'sheet_name': row['sheet_name'],
                'table_name': row['table_name']
            })
        
        for col_name, tables in column_groups.items():
            print(f"\n'{col_name}' 列出现在 {len(tables)} 个表中:")
            for table_info in tables:
                print(f"  - {table_info['sheet_name']} → {table_info['table_name']}")
        
        # 4. 实际应用示例
        print(f"\n4. 实际应用示例:")
        print("-" * 40)
        
        # 示例1: 通过 Pin Name 关联查询
        print("\n示例1: 通过 'Pin Name' 进行关联查询")
        print("查询引脚 'PA0' 在所有相关表中的信息:")
        
        # 获取包含 Pin Name 的所有表
        pin_name_tables = [row for row in relationships if row['column_name'] == 'Pin Name']
        
        for table_info in pin_name_tables:
            table_name = table_info['table_name']
            sheet_name = table_info['sheet_name']
            
            # 查询该表中 PA0 的信息
            query = f"SELECT * FROM {table_name} WHERE Pin_Name = 'PA0'"
            result = db.execute_custom_query(query)
            
            if result:
                print(f"  {sheet_name}:")
                record = result[0]
                # 只显示有值的字段
                for key, value in record.items():
                    if key != 'id' and value and str(value).strip():
                        print(f"    {key}: {value}")
            else:
                print(f"  {sheet_name}: 无数据")
        
        # 示例2: 通过 EXTI ID 关联查询
        print(f"\n示例2: 通过 'EXTI ID' 进行关联查询")
        print("查询 EXTI ID '0' 的相关信息:")
        
        exti_tables = [row for row in relationships if row['column_name'] == 'EXTI ID']
        
        for table_info in exti_tables:
            table_name = table_info['table_name']
            sheet_name = table_info['sheet_name']
            
            # 查询该表中 EXTI ID 0 的信息
            if table_name == 'sheet_GPIO_input_set':
                query = f"SELECT * FROM {table_name} WHERE EXTI_ID = '0'"
            else:
                query = f"SELECT * FROM {table_name} WHERE EXTI_ID = '0'"
            
            result = db.execute_custom_query(query)
            
            if result:
                print(f"  {sheet_name}:")
                record = result[0]
                for key, value in record.items():
                    if key != 'id' and value and str(value).strip():
                        print(f"    {key}: {value}")
            else:
                print(f"  {sheet_name}: 无数据")
        
        # 5. 在代码中的使用
        print(f"\n5. 在代码中的使用:")
        print("-" * 40)
        print("数据库管理器使用这个表来:")
        print("✓ 自动加载列关联关系 (_load_column_relationships 方法)")
        print("✓ 实现 query_by_pin_name 方法的跨表查询")
        print("✓ 提供 get_column_relationships_info 方法")
        print("✓ 支持智能的数据关联和查询优化")
        
        # 6. 数据一致性检查
        print(f"\n6. 数据一致性检查:")
        print("-" * 40)
        
        # 检查 Pin Name 的一致性
        pin_names_in_definitions = set()
        pin_names_in_af = set()
        
        # 从 GPIO Pin definitions 获取所有引脚名
        pin_defs = db.execute_custom_query("SELECT DISTINCT Pin_Name FROM sheet_GPIO_Pin_definitions WHERE Pin_Name IS NOT NULL")
        pin_names_in_definitions = {row['Pin_Name'] for row in pin_defs}
        
        # 从 GPIO_AF 获取所有引脚名
        af_pins = db.execute_custom_query("SELECT DISTINCT Pin_Name FROM sheet_GPIO_AF WHERE Pin_Name IS NOT NULL")
        pin_names_in_af = {row['Pin_Name'] for row in af_pins}
        
        # 检查一致性
        common_pins = pin_names_in_definitions & pin_names_in_af
        only_in_definitions = pin_names_in_definitions - pin_names_in_af
        only_in_af = pin_names_in_af - pin_names_in_definitions
        
        print(f"Pin Name 一致性检查:")
        print(f"  GPIO Pin definitions 中的引脚数: {len(pin_names_in_definitions)}")
        print(f"  GPIO_AF 中的引脚数: {len(pin_names_in_af)}")
        print(f"  共同的引脚数: {len(common_pins)}")
        
        if only_in_definitions:
            print(f"  只在 definitions 中的引脚: {len(only_in_definitions)} 个")
            if len(only_in_definitions) <= 5:
                print(f"    {list(only_in_definitions)}")
        
        if only_in_af:
            print(f"  只在 AF 中的引脚: {len(only_in_af)} 个")
            if len(only_in_af) <= 5:
                print(f"    {list(only_in_af)}")
        
        # 7. 总结
        print(f"\n7. 总结:")
        print("-" * 40)
        print("column_relationships 表是数据库系统的核心组件，它:")
        print("✓ 提供了表间关联的元数据")
        print("✓ 使得跨表查询变得简单和高效")
        print("✓ 支持数据一致性检查")
        print("✓ 为用户提供了直观的数据关联视图")
        print("✓ 是实现复杂查询功能的基础")
        
    finally:
        db.close()

if __name__ == "__main__":
    main()
