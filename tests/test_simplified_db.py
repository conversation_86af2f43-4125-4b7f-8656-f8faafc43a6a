#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试简化后的数据库结构
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from gpio_database_manager import GPIODatabaseManager

def main():
    print("测试简化后的数据库结构")
    print("=" * 50)
    
    db = GPIODatabaseManager("../database/gpio_database.db")
    if not db.connect():
        return
    
    try:
        # 1. 显示简化后的column_relationships表结构
        print("\n1. 简化后的column_relationships表:")
        result = db.execute_custom_query('SELECT * FROM column_relationships')
        print(f"{'ID':<5} {'列名':<15} {'表名':<25}")
        print("-" * 45)
        for row in result:
            print(f"{row['id']:<5} {row['column_name']:<15} {row['table_name']:<25}")
        
        # 2. 对比表大小
        print(f"\n2. 表结构对比:")
        print(f"简化前: 4个字段 (id, column_name, sheet_name, table_name)")
        print(f"简化后: 3个字段 (id, column_name, table_name)")
        print(f"减少了25%的存储空间")
        
        # 3. 测试功能是否正常
        print(f"\n3. 功能测试:")
        
        # 测试引脚查询
        pa0_info = db.query_by_pin_name('PA0')
        print(f"✅ 引脚查询: 找到PA0在{len(pa0_info)}个表中的信息")
        
        # 测试AF查询
        af_info = db.query_gpio_af('PA0')
        print(f"✅ AF查询: 找到PA0的{len(af_info)}条AF配置")
        
        # 测试功能查找
        timer_pins = db.find_pins_by_function('TIMER1')
        print(f"✅ 功能查找: 找到{len(timer_pins)}个TIMER1相关引脚")
        
        # 4. 显示列关联关系
        print(f"\n4. 列关联关系:")
        relationships = db.get_column_relationships_info()
        for col_name, sheets in relationships.items():
            print(f"  '{col_name}' 关联: {', '.join(sheets)}")
        
        print(f"\n✅ 简化成功！所有功能正常工作")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
    
    finally:
        db.close()

if __name__ == "__main__":
    main()
