#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证数据库清理结果
"""

from gpio_database_manager import GPIODatabaseManager

def main():
    db = GPIODatabaseManager()
    if not db.connect():
        return
    
    try:
        # 检查所有表中是否还有 _x000D_ 字符
        tables_to_check = [
            'sheet_GPIO_Pin_definitions',
            'sheet_GPIO_AF',
            'sheet_EXTI'
        ]
        
        total_problematic = 0
        
        for table in tables_to_check:
            print(f"检查表: {table}")
            
            # 获取表结构
            table_info = db.get_table_info(table)
            if table not in table_info:
                continue
                
            columns = [col['name'] for col in table_info[table]['columns'] if col['name'] != 'id']
            
            for column in columns:
                # 检查每一列
                query = f"SELECT COUNT(*) as count FROM {table} WHERE {column} LIKE '%_x000D_%'"
                result = db.execute_custom_query(query)
                
                if result and result[0]['count'] > 0:
                    count = result[0]['count']
                    total_problematic += count
                    print(f"  {column}: {count} 条记录包含 _x000D_")
                    
                    # 显示具体的问题记录
                    query2 = f"SELECT {column} FROM {table} WHERE {column} LIKE '%_x000D_%' LIMIT 3"
                    examples = db.execute_custom_query(query2)
                    for example in examples:
                        print(f"    示例: {example[column]}")
        
        if total_problematic == 0:
            print("\n✅ 验证完成：数据库中没有发现 _x000D_ 字符！")
        else:
            print(f"\n❌ 发现 {total_problematic} 条记录仍包含 _x000D_ 字符")
            
        # 额外检查：显示一些示例数据
        print("\n示例数据检查:")
        sample_pins = ['PA0', 'PE8', 'PD15']
        for pin in sample_pins:
            pin_info = db.query_by_pin_name(pin)
            if pin_info and 'GPIO Pin definitions' in pin_info:
                pin_data = pin_info['GPIO Pin definitions'][0]
                print(f"  {pin}: 引脚名='{pin_data.get('Pin_Name')}', 用途='{pin_data.get('Usage')}', PCB='{pin_data.get('PCB_NAME')}'")
    
    finally:
        db.close()

if __name__ == "__main__":
    main()
